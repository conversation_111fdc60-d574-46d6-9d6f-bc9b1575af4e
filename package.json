{"name": "rainbow-station-inc-pos", "productName": "rainbow-station-inc-pos", "version": "1.0.0", "description": "My Electron application description", "main": "src/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "electron-squirrel-startup": "^1.0.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "electron": "37.2.0"}}