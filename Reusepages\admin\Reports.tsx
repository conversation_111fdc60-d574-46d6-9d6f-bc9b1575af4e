import { Button } from "@/components/ui/button"

export default function Reports() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Reports & Analytics</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-green-600 mb-4">Sales Reports</h3>
          <p className="text-gray-600 mb-4">Daily, weekly, and monthly sales analytics</p>
          <div className="space-y-2">
            <Button className="bg-green-600 hover:bg-green-700 text-white font-bold w-full">
              Daily Sales Report
            </Button>
            <Button className="bg-green-500 hover:bg-green-600 text-white font-bold w-full">
              Weekly Sales Report
            </Button>
            <Button className="bg-green-400 hover:bg-green-500 text-white font-bold w-full">
              Monthly Sales Report
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-blue-600 mb-4">Inventory Reports</h3>
          <p className="text-gray-600 mb-4">Stock levels and inventory movement reports</p>
          <div className="space-y-2">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold w-full">
              Current Stock Report
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white font-bold w-full">
              Low Stock Alert
            </Button>
            <Button className="bg-blue-400 hover:bg-blue-500 text-white font-bold w-full">
              Inventory Movement
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-yellow-600 mb-4">Customer Reports</h3>
          <p className="text-gray-600 mb-4">Customer analytics and behavior reports</p>
          <div className="space-y-2">
            <Button className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold w-full">
              Customer Analysis
            </Button>
            <Button className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold w-full">
              Purchase History
            </Button>
            <Button className="bg-yellow-400 hover:bg-yellow-500 text-white font-bold w-full">
              Customer Trends
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-red-600 mb-4">Financial Reports</h3>
          <p className="text-gray-600 mb-4">Revenue, profit, and financial analytics</p>
          <div className="space-y-2">
            <Button className="bg-red-600 hover:bg-red-700 text-white font-bold w-full">
              Revenue Report
            </Button>
            <Button className="bg-red-500 hover:bg-red-600 text-white font-bold w-full">
              Profit & Loss
            </Button>
            <Button className="bg-red-400 hover:bg-red-500 text-white font-bold w-full">
              Tax Report
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-purple-600 mb-4">Product Reports</h3>
          <p className="text-gray-600 mb-4">Product performance and category analysis</p>
          <div className="space-y-2">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white font-bold w-full">
              Top Products
            </Button>
            <Button className="bg-purple-500 hover:bg-purple-600 text-white font-bold w-full">
              Category Performance
            </Button>
            <Button className="bg-purple-400 hover:bg-purple-500 text-white font-bold w-full">
              Product Trends
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-indigo-600 mb-4">Custom Reports</h3>
          <p className="text-gray-600 mb-4">Create and manage custom report templates</p>
          <div className="space-y-2">
            <Button className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold w-full">
              Report Builder
            </Button>
            <Button className="bg-indigo-500 hover:bg-indigo-600 text-white font-bold w-full">
              Saved Reports
            </Button>
            <Button className="bg-indigo-400 hover:bg-indigo-500 text-white font-bold w-full">
              Schedule Reports
            </Button>
          </div>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-black text-green-600">$12,450</div>
            <div className="text-sm text-gray-600">This Week Sales</div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-black text-blue-600">342</div>
            <div className="text-sm text-gray-600">Products Sold</div>
          </div>
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-black text-yellow-600">89</div>
            <div className="text-sm text-gray-600">Active Customers</div>
          </div>
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-2xl font-black text-red-600">15</div>
            <div className="text-sm text-gray-600">Low Stock Items</div>
          </div>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Recent Report Activity</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
            <div>
              <span className="text-green-700 font-medium">Daily Sales Report Generated</span>
              <div className="text-sm text-gray-500">Report ID: RPT-2024-001</div>
            </div>
            <span className="text-gray-500 text-sm">5 minutes ago</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
            <div>
              <span className="text-blue-700 font-medium">Inventory Report Scheduled</span>
              <div className="text-sm text-gray-500">Next run: Tomorrow 9:00 AM</div>
            </div>
            <span className="text-gray-500 text-sm">1 hour ago</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
            <div>
              <span className="text-yellow-700 font-medium">Customer Analysis Completed</span>
              <div className="text-sm text-gray-500">Report ID: RPT-2024-002</div>
            </div>
            <span className="text-gray-500 text-sm">3 hours ago</span>
          </div>
        </div>
      </div>
    </div>
  )
}
